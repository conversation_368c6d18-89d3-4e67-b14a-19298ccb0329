<!-- 仓库管理系统首页 -->
<route lang="json5">
{
  layout: 'tabbar',
  needLogin: true,
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '仓库管理系统',
  },
}
</route>
<template>
  <view
    class="warehouse-home bg-gray-50 min-h-screen"
    :style="{ paddingTop: safeAreaInsets?.top + 'px' }"
  >
    <!-- 顶部标题栏 -->
    <view class="header bg-white px-4 py-4 shadow-sm">
      <view class="text-center text-2xl font-bold text-gray-800">仓库管理系统</view>
    </view>

    <!-- 功能卡片区域 -->
    <view class="function-cards px-4 py-6">
      <wd-row :gutter="16">
        <!-- 装箱功能卡片 -->
        <wd-col :span="12">
          <view class="function-card bg-white rounded-lg shadow-md p-6 text-center" @click="goToPacking">
            <view class="icon-container mb-4">
              <view class="icon-box w-16 h-16 mx-auto bg-blue-100 rounded-full flex items-center justify-center">
                <wd-icon name="package" size="32px" color="#3b82f6"></wd-icon>
              </view>
            </view>
            <view class="text-lg font-semibold text-gray-800 mb-2">装箱</view>
            <view class="text-sm text-gray-500">产品装箱作业</view>
          </view>
        </wd-col>

        <!-- 出库功能卡片 -->
        <wd-col :span="12">
          <view class="function-card bg-white rounded-lg shadow-md p-6 text-center" @click="goToOutbound">
            <view class="icon-container mb-4">
              <view class="icon-box w-16 h-16 mx-auto bg-green-100 rounded-full flex items-center justify-center">
                <wd-icon name="truck" size="32px" color="#10b981"></wd-icon>
              </view>
            </view>
            <view class="text-lg font-semibold text-gray-800 mb-2">出库</view>
            <view class="text-sm text-gray-500">产品出库作业</view>
          </view>
        </wd-col>
      </wd-row>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { useToast } from 'wot-design-uni'

defineOptions({
  name: 'WarehouseHome',
})

const toast = useToast()
// 获取屏幕边界到安全区域距离
const { safeAreaInsets } = uni.getSystemInfoSync()

// 跳转到装箱页面
const goToPacking = () => {
  uni.navigateTo({
    url: '/pages/packing/packing'
  })
}

// 跳转到出库页面
const goToOutbound = () => {
  uni.navigateTo({
    url: '/pages/outbound/outbound'
  })
}
</script>

<style scoped>
.warehouse-home {
  background-color: #f9fafb;
}

.header {
  border-bottom: 1px solid #e5e7eb;
}

.function-card {
  transition: all 0.3s ease;
  border: 1px solid #e5e7eb;
}

.function-card:active {
  transform: scale(0.98);
  background-color: #f8fafc;
}

.icon-box {
  transition: all 0.3s ease;
}

.function-card:active .icon-box {
  transform: scale(1.1);
}
</style>
